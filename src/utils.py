# src/utils.py

def check_ollama_status():
    # Dummy status for testing
    return {
        "status": "running",
        "text_model_available": True,
        "embedding_model_available": True,
        "available_models": ["llama3.1:latest", "nomic-embed-text"]
    }

def validate_uploaded_file(uploaded_file):
    # Always valid
    return {"valid": True, "error": ""}

def save_uploaded_file(uploaded_file):
    # Return fake file path
    return f"/tmp/{uploaded_file.name}"

def display_error_message(e, context=""):
    import streamlit as st
    st.error(f"Error: {context}: {str(e)}")

def display_success_message(msg):
    import streamlit as st
    st.success(msg)

def load_css_style():
    # No custom CSS by default
    return ""

def format_file_size(size):
    # Return as KB/MB string
    if size < 1024:
        return f"{size} bytes"
    elif size < 1024**2:
        return f"{size/1024:.2f} KB"
    else:
        return f"{size/1024**2:.2f} MB"

def clean_upload_directory():
    # No real cleanup (stub)
    pass
