import chromadb

class ChromaVectorStore:
    def __init__(self, persist_directory="chroma_db"):
        self.client = chromadb.PersistentClient(path=persist_directory)
        self.collection = self.client.get_or_create_collection("pdf_documents")

    def add_documents(self, documents):
        texts = [doc["page_content"] for doc in documents]
        metadatas = [doc["metadata"] for doc in documents]
        ids = [f"doc_{i}" for i in range(len(texts))]
        self.collection.add(documents=texts, metadatas=metadatas, ids=ids)
        return ids

    def similarity_search(self, query_embedding, k=4):
        results = self.collection.query(query_embeddings=[query_embedding], n_results=k)
        docs = []
        for i in range(len(results["documents"][0])):
            docs.append({
                "page_content": results["documents"][0][i],
                "metadata": results["metadatas"][0][i]
            })
        return docs

    def get_collection_info(self):
        return {"collection_name": self.collection.name, "document_count": self.collection.count()}

def create_vector_store():
    return ChromaVectorStore()
