import chromadb
import config
import uuid
from datetime import datetime
from src.embeddings import EmbeddingManager

class ChromaVectorStore:
    def __init__(self, persist_directory=None):
        if persist_directory is None:
            persist_directory = str(config.CHROMA_DB_DIR)
        self.client = chromadb.PersistentClient(path=persist_directory)
        self.embedding_manager = EmbeddingManager()

        # Create collection with custom embedding function
        self.collection = self.client.get_or_create_collection(
            name=config.COLLECTION_NAME,
            embedding_function=self._get_embedding_function(),
            metadata={"hnsw:space": "cosine"}  # Use cosine similarity
        )

    def _get_embedding_function(self):
        """Create a custom embedding function for ChromaDB that uses Ollama"""
        class OllamaEmbeddingFunction:
            def __init__(self, embedding_manager):
                self.embedding_manager = embedding_manager
                self.name = "ollama_embeddings"  # Required by ChromaDB

            def __call__(self, input):
                # ChromaDB expects a list of texts and returns a list of embeddings
                if isinstance(input, str):
                    input = [input]
                return self.embedding_manager.embed_text(input)

        return OllamaEmbeddingFunction(self.embedding_manager)

    def add_documents(self, documents):
        """Add documents to the vector store"""
        texts = [doc["page_content"] for doc in documents]
        metadatas = [doc["metadata"] for doc in documents]

        # Generate unique IDs using timestamp and UUID
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        ids = [f"{timestamp}_{uuid.uuid4().hex[:8]}_{i}" for i in range(len(texts))]

        try:
            self.collection.add(
                documents=texts,
                metadatas=metadatas,
                ids=ids
            )
            return ids
        except Exception as e:
            raise Exception(f"Error adding documents to vector store: {str(e)}")

    def similarity_search(self, query_text, k=4):
        """Search for similar documents using query text (not embedding)"""
        try:
            results = self.collection.query(
                query_texts=[query_text],  # Use query_texts instead of query_embeddings
                n_results=k
            )

            docs = []
            if results["documents"] and len(results["documents"]) > 0:
                for i in range(len(results["documents"][0])):
                    docs.append({
                        "page_content": results["documents"][0][i],
                        "metadata": results["metadatas"][0][i] if results["metadatas"] else {}
                    })
            return docs
        except Exception as e:
            raise Exception(f"Error searching vector store: {str(e)}")

    def get_collection_info(self):
        return {"collection_name": self.collection.name, "document_count": self.collection.count()}

    def reset_collection(self):
        """Delete and recreate the collection to start fresh"""
        try:
            # Delete the existing collection
            self.client.delete_collection(name=config.COLLECTION_NAME)
        except Exception:
            # Collection might not exist, that's fine
            pass

        # Recreate the collection
        self.collection = self.client.get_or_create_collection(
            name=config.COLLECTION_NAME,
            embedding_function=self._get_embedding_function(),
            metadata={"hnsw:space": "cosine"}
        )

def create_vector_store():
    return ChromaVectorStore()
