import requests
import config

class EmbeddingManager:
    def embed_text(self, texts):
        if isinstance(texts, str):
            texts = [texts]
        url = f"{config.OLLAMA_BASE_URL}/api/embeddings"
        payload = {"model": config.OLLAMA_EMBEDDING_MODEL, "prompt": texts}
        r = requests.post(url, json=payload, timeout=60)
        data = r.json()
        if "embedding" in data:
            return [data["embedding"]]
        elif "data" in data:
            return [item["embedding"] for item in data["data"]]
        else:
            raise RuntimeError(f"Embedding error: {data}")

    def test_embedding(self, text="This is a test sentence."):
        try:
            self.embed_text(text)
            return True
        except Exception as e:
            print(f"Embedding test failed: {e}")
            return False

    def get_embedding_dimension(self):
        try:
            return len(self.embed_text("test")[0])
        except Exception:
            return 0

def check_ollama_models():
    return True
