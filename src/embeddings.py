import requests
import config

class EmbeddingManager:
    def embed_text(self, texts):
        """
        Generate embeddings for text(s) using Ollama.
        Returns a list of embeddings.
        """
        # Handle single text or list of texts
        if isinstance(texts, str):
            single_text = True
            text_list = [texts]
        else:
            single_text = False
            text_list = texts

        embeddings = []
        url = f"{config.OLLAMA_BASE_URL}/api/embeddings"

        # Process each text individually since Ollama expects single strings
        for text in text_list:
            payload = {"model": config.OLLAMA_EMBEDDING_MODEL, "prompt": text}
            try:
                r = requests.post(url, json=payload, timeout=60)
                r.raise_for_status()  # Raise an exception for bad status codes
                data = r.json()

                if "embedding" in data:
                    embeddings.append(data["embedding"])
                else:
                    raise RuntimeError(f"Embedding error: {data}")

            except requests.exceptions.RequestException as e:
                raise RuntimeError(f"Request error: {e}")

        return embeddings

    def test_embedding(self, text="This is a test sentence."):
        try:
            self.embed_text(text)
            return True
        except Exception as e:
            print(f"Embedding test failed: {e}")
            return False

    def get_embedding_dimension(self):
        try:
            return len(self.embed_text("test")[0])
        except Exception:
            return 0

def check_ollama_models():
    return True
