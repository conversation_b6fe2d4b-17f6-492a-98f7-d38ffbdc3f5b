from PyPDF2 import PdfReader
from pathlib import Path
import config

class PDFProcessor:
    def __init__(self, chunk_size=None, chunk_overlap=None):
        self.chunk_size = chunk_size or config.CHUNK_SIZE
        self.chunk_overlap = chunk_overlap or config.CHUNK_OVERLAP

    def process_pdf(self, file_path):
        """
        Accepts a file path string and extracts and chunks text from the PDF.
        Returns a list of document chunks with metadata.
        """
        try:
            # Convert to Path object if it's a string
            if isinstance(file_path, str):
                file_path = Path(file_path)

            # Read the PDF file
            with open(file_path, 'rb') as file:
                reader = PdfReader(file)
                all_text = []

                for page_num, page in enumerate(reader.pages):
                    text = page.extract_text()
                    if text.strip():  # Only add non-empty text
                        all_text.append(text)

                if not all_text:
                    raise ValueError("No text content found in PDF")

                full_text = "\n".join(all_text)

                # Create chunks
                chunks = []
                start = 0
                chunk_id = 0

                while start < len(full_text):
                    end = start + self.chunk_size
                    chunk_text = full_text[start:end]

                    # Create document chunk with metadata
                    chunks.append({
                        "page_content": chunk_text,
                        "metadata": {
                            "source": str(file_path.name),
                            "chunk_id": chunk_id,
                            "start_char": start,
                            "end_char": min(end, len(full_text)),
                            "total_pages": len(reader.pages)
                        }
                    })

                    chunk_id += 1
                    start += self.chunk_size - self.chunk_overlap

                return chunks

        except Exception as e:
            raise Exception(f"Error processing PDF {file_path}: {str(e)}")

def validate_pdf_file(file_path):
    """
    Accepts a file path string and checks if it is a valid PDF.
    Returns True if valid, False otherwise.
    """
    try:
        # Convert to Path object if it's a string
        if isinstance(file_path, str):
            file_path = Path(file_path)

        # Check if file exists
        if not file_path.exists():
            print(f"PDF validation error: File does not exist: {file_path}")
            return False

        # Try to read the PDF
        with open(file_path, 'rb') as file:
            reader = PdfReader(file)
            # Try to access the first page to ensure it's a valid PDF
            if len(reader.pages) == 0:
                print("PDF validation error: PDF has no pages")
                return False

            # Try to extract text from first page
            first_page = reader.pages[0]
            first_page.extract_text()

        return True

    except Exception as e:
        print(f"PDF validation error: {e}")
        return False
