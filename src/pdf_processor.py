from PyPDF2 import PdfReader

class PDFProcessor:
    def __init__(self, chunk_size=1000, chunk_overlap=200):
        self.chunk_size = chunk_size
        self.chunk_overlap = chunk_overlap

    def process_pdf(self, file):
        """
        Accepts a file-like object (as from <PERSON>lit's file uploader).
        Extracts and chunks text.
        """
        file.seek(0)
        reader = PdfReader(file)
        all_text = []
        for page in reader.pages:
            text = page.extract_text()
            if text:
                all_text.append(text)
        full_text = "\n".join(all_text)
        # Chunking
        chunks = []
        start = 0
        while start < len(full_text):
            chunk = full_text[start:start+self.chunk_size]
            chunks.append({
                "page_content": chunk,
                "metadata": {"start": start, "end": start+len(chunk)}
            })
            start += self.chunk_size - self.chunk_overlap
        return chunks

def validate_pdf_file(file):
    """
    Accepts a file-like object and checks if it is a valid PDF.
    """
    from PyPDF2 import PdfReader
    try:
        file.seek(0)
        PdfReader(file)
        file.seek(0)
        return True
    except Exception as e:
        print("PDF validation error:", e)
        return False
