import requests
import config
from src.embeddings import EmbeddingManager

class RAGChatbot:
    def __init__(self, vector_store):
        self.vector_store = vector_store
        self.embedding_manager = EmbeddingManager()
        self.memory = []

    def chat(self, prompt):
        # Use text-based similarity search (ChromaDB will handle embeddings internally)
        docs = self.vector_store.similarity_search(prompt, k=config.SIMILARITY_SEARCH_K)
        context = "\n\n".join([d["page_content"] for d in docs])
        answer = self.llm_answer(prompt, context)
        return {"answer": answer, "sources": docs}

    def llm_answer(self, prompt, context):
        url = f"{config.OLLAMA_BASE_URL}/api/generate"
        user_prompt = (
            "Answer the following question using the provided context:\n\n"
            f"Context:\n{context}\n\n"
            f"Question: {prompt}\nAnswer:"
        )
        payload = {"model": config.OLLAMA_MODEL, "prompt": user_prompt}
        r = requests.post(url, json=payload, timeout=90)
        data = r.json()
        return data.get("response", "[No answer returned]")

    def summarize_documents(self, max_docs=3):
        all_chunks = self.vector_store.collection.get(limit=max_docs)
        texts = [doc for doc in all_chunks['documents']]
        context = "\n\n".join(texts)
        return self.llm_summarize(context)

    def llm_summarize(self, context):
        url = f"{config.OLLAMA_BASE_URL}/api/generate"
        user_prompt = (
            "Summarize the following document content:\n\n"
            f"{context}\n\nSummary:"
        )
        payload = {"model": config.OLLAMA_MODEL, "prompt": user_prompt}
        r = requests.post(url, json=payload, timeout=90)
        data = r.json()
        return data.get("response", "[No summary returned]")

    def clear_memory(self):
        self.memory = []

def create_rag_chatbot(vector_store):
    return RAGChatbot(vector_store)
