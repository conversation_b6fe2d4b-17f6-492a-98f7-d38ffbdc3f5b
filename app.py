"""
Main Streamlit Application for RAG with Ollama
"""

import streamlit as st
import logging
from pathlib import Path
import time

# --- Attempt critical imports, fail gracefully if missing ---
try:
    from src.pdf_processor import PDFProcessor, validate_pdf_file
    from src.embeddings import Embedding<PERSON>anager, check_ollama_models
    from src.vector_store import ChromaVectorStore, create_vector_store
    from src.retrieval_qa import RAGChatbot, create_rag_chatbot
    from src.utils import (
        check_ollama_status, validate_uploaded_file, save_uploaded_file,
        display_error_message, display_success_message, load_css_style,
        format_file_size, clean_upload_directory
    )
    import config
except Exception as e:
    st.error(f"❌ Critical import error: {e}")
    st.stop()

# Set up logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# --- Page configuration ---
st.set_page_config(
    page_title=getattr(config, "PAGE_TITLE", "RAG Chat with <PERSON>lla<PERSON>"),
    page_icon=getattr(config, "PAGE_ICON", "📚"),
    layout=getattr(config, "LAYOUT", "wide"),
    initial_sidebar_state="expanded"
)

# --- Load custom CSS (don't fail if not found) ---
try:
    st.markdown(load_css_style(), unsafe_allow_html=True)
except Exception:
    pass  # Don't block app if CSS missing

# --- Session State Initialization ---
def initialize_session_state():
    """Initialize Streamlit session state variables"""
    defaults = {
        'vector_store': None,
        'chatbot': None,
        'chat_history': [],
        'processed_files': [],
        'ollama_status': None
    }
    for k, v in defaults.items():
        if k not in st.session_state:
            st.session_state[k] = v

# --- Ollama connection check ---
def check_ollama_connection():
    """Check and display Ollama connection status"""
    status = check_ollama_status()
    st.session_state.ollama_status = status

    if status['status'] == 'running':
        st.success("✅ Ollama is running")

        # Check required models
        text_model_ok = status.get('text_model_available', False)
        embed_model_ok = status.get('embedding_model_available', False)

        col1, col2 = st.columns(2)
        with col1:
            if text_model_ok:
                st.success(f"✅ Text model: {config.OLLAMA_MODEL}")
            else:
                st.error(f"❌ Text model missing: {config.OLLAMA_MODEL}")
                st.code(f"ollama pull {config.OLLAMA_MODEL}")

        with col2:
            if embed_model_ok:
                st.success(f"✅ Embedding model: {config.OLLAMA_EMBEDDING_MODEL}")
            else:
                st.error(f"❌ Embedding model missing: {config.OLLAMA_EMBEDDING_MODEL}")
                st.code(f"ollama pull {config.OLLAMA_EMBEDDING_MODEL}")

        return text_model_ok and embed_model_ok

    else:
        st.error(f"❌ Ollama not available: {status.get('error', 'Unknown error')}")
        st.info("Make sure Ollama is running: `ollama serve`")
        return False

# --- Vector Store Setup ---
def setup_vector_store():
    try:
        if st.session_state.vector_store is None:
            with st.spinner("Initializing vector store..."):
                st.session_state.vector_store = create_vector_store()
            st.success("Vector store initialized successfully!")
        return True
    except Exception as e:
        display_error_message(e, "vector store initialization")
        return False

# --- Chatbot Setup ---
def setup_chatbot():
    try:
        if st.session_state.chatbot is None and st.session_state.vector_store is not None:
            with st.spinner("Initializing RAG chatbot..."):
                st.session_state.chatbot = create_rag_chatbot(st.session_state.vector_store)
            st.success("RAG chatbot initialized successfully!")
        return st.session_state.chatbot is not None
    except Exception as e:
        display_error_message(e, "chatbot initialization")
        return False

# --- File Upload Section ---
def file_upload_section():
    st.header("📁 Document Upload")
    uploaded_files = st.file_uploader(
        "Upload PDF documents",
        type=['pdf'],
        accept_multiple_files=True,
        help=f"Maximum file size: {config.MAX_FILE_SIZE_MB} MB per file"
    )

    if uploaded_files:
        for uploaded_file in uploaded_files:
            # Validate file
            validation = validate_uploaded_file(uploaded_file)
            if not validation['valid']:
                st.error(f"❌ {uploaded_file.name}: {validation['error']}")
                continue
            if uploaded_file.name in st.session_state.processed_files:
                st.info(f"📄 {uploaded_file.name} already processed")
                continue
            if st.button(f"Process {uploaded_file.name}", key=f"process_{uploaded_file.name}"):
                process_uploaded_file(uploaded_file)

def process_uploaded_file(uploaded_file):
    try:
        with st.spinner(f"Saving {uploaded_file.name}..."):
            file_path = save_uploaded_file(uploaded_file)
        if not validate_pdf_file(file_path):
            st.error(f"❌ Invalid PDF file: {uploaded_file.name}")
            return
        with st.spinner(f"Processing {uploaded_file.name}..."):
            processor = PDFProcessor()
            documents = processor.process_pdf(file_path)
        st.success(f"✅ Extracted {len(documents)} chunks from {uploaded_file.name}")
        if st.session_state.vector_store is not None:
            with st.spinner("Adding to vector store..."):
                ids = st.session_state.vector_store.add_documents(documents)
            st.success(f"✅ Added {len(ids)} document chunks to vector store")
            st.session_state.processed_files.append(uploaded_file.name)
            st.session_state.chatbot = None
            setup_chatbot()
    except Exception as e:
        display_error_message(e, f"processing {uploaded_file.name}")

# --- Chat Interface ---
def chat_interface():
    st.header("💬 Chat with Documents")
    if not st.session_state.processed_files:
        st.info("📚 Upload and process some documents first to start chatting!")
        return
    if st.session_state.chatbot is None:
        st.error("❌ Chatbot not initialized. Please check Ollama connection and try again.")
        return
    for i, message in enumerate(st.session_state.chat_history):
        if message['type'] == 'user':
            with st.chat_message("user"):
                st.write(message['content'])
        else:
            with st.chat_message("assistant"):
                st.write(message['content'])
                if 'sources' in message and message['sources']:
                    with st.expander(f"📚 Sources ({len(message['sources'])})"):
                        for j, source in enumerate(message['sources']):
                            st.markdown(f"**Source {j+1}:**")
                            st.markdown(f"```\n{source['page_content']}\n```")
                            if 'metadata' in source:
                                st.json(source['metadata'])
    # Chat input
    if prompt := st.chat_input("Ask a question about your documents..."):
        st.session_state.chat_history.append({'type': 'user','content': prompt})
        with st.chat_message("user"):
            st.write(prompt)
        with st.chat_message("assistant"):
            with st.spinner("Thinking..."):
                response = st.session_state.chatbot.chat(prompt)
            st.write(response['answer'])
            if response['sources']:
                with st.expander(f"📚 Sources ({len(response['sources'])})"):
                    for i, source in enumerate(response['sources']):
                        st.markdown(f"**Source {i+1}:**")
                        st.markdown(f"```\n{source['page_content']}\n```")
                        if 'metadata' in source:
                            st.json(source['metadata'])
        st.session_state.chat_history.append({
            'type': 'assistant',
            'content': response['answer'],
            'sources': response['sources']
        })

# --- Summarization Section ---
def summarization_section():
    st.header("📋 Document Summary")
    if not st.session_state.processed_files:
        st.info("📚 Upload and process some documents first to generate a summary!")
        return
    if st.session_state.chatbot is None:
        st.error("❌ Chatbot not initialized. Please check Ollama connection and try again.")
        return
    if st.button("Generate Summary", type="primary"):
        with st.spinner("Generating summary..."):
            try:
                summary = st.session_state.chatbot.summarize_documents()
                st.markdown("### 📄 Document Summary")
                st.markdown(summary)
            except Exception as e:
                display_error_message(e, "summary generation")

# --- Sidebar Info ---
def sidebar_info():
    st.sidebar.header("📊 System Status")
    # Ollama status
    if st.sidebar.button("🔄 Refresh Status"):
        st.session_state.ollama_status = None
        st.rerun()
    if st.session_state.ollama_status:
        status = st.session_state.ollama_status
        if status['status'] == 'running':
            st.sidebar.success("✅ Ollama Running")
        else:
            st.sidebar.error("❌ Ollama Not Available")
    # Vector store info
    if st.session_state.vector_store:
        try:
            info = st.session_state.vector_store.get_collection_info()
            st.sidebar.info(f"📚 Documents: {info.get('document_count', 0)}")
        except:
            st.sidebar.warning("⚠️ Vector store info unavailable")
    # Current config (debugging/tuning)
    st.sidebar.header("⚙️ Current Config")
    st.sidebar.markdown(f"**Text Model:** `{getattr(config, 'OLLAMA_MODEL', '')}`")
    st.sidebar.markdown(f"**Embedding Model:** `{getattr(config, 'OLLAMA_EMBEDDING_MODEL', '')}`")
    st.sidebar.markdown(f"**Chunk Size:** `{getattr(config, 'CHUNK_SIZE', 1000)}`")
    st.sidebar.markdown(f"**Chunk Overlap:** `{getattr(config, 'CHUNK_OVERLAP', 200)}`")
    st.sidebar.markdown(f"**Max Tokens:** `{getattr(config, 'MAX_TOKENS', 4000)}`")
    # Processed files
    st.sidebar.header("📁 Processed Files")
    if st.session_state.processed_files:
        for filename in st.session_state.processed_files:
            st.sidebar.text(f"📄 {filename}")
    else:
        st.sidebar.info("No files processed yet")
    # Clear data
    st.sidebar.header("🗑️ Clear Data")
    if st.sidebar.button("Clear Chat History"):
        st.session_state.chat_history = []
        if st.session_state.chatbot:
            st.session_state.chatbot.clear_memory()
        st.rerun()
    if st.sidebar.button("Clear All Data", type="secondary"):
        if st.sidebar.checkbox("I understand this will delete all data"):
            # Reset vector store
            if st.session_state.vector_store:
                try:
                    st.session_state.vector_store.reset_collection()
                except Exception as e:
                    st.error(f"Error resetting vector store: {e}")

            # Clear session state
            st.session_state.vector_store = None
            st.session_state.chatbot = None
            st.session_state.chat_history = []
            st.session_state.processed_files = []
            clean_upload_directory()
            st.success("All data cleared successfully!")
            st.rerun()

# --- Main App ---
def main():
    initialize_session_state()
    st.markdown('<h1 class="main-header">🤖 RAG Chat with Ollama</h1>', unsafe_allow_html=True)
    st.markdown("---")
    ollama_ok = check_ollama_connection()
    if not ollama_ok:
        st.stop()
    vector_store_ok = setup_vector_store()
    if vector_store_ok:
        setup_chatbot()
    tab1, tab2, tab3 = st.tabs(["📁 Upload", "💬 Chat", "📋 Summary"])
    with tab1:
        file_upload_section()
    with tab2:
        chat_interface()
    with tab3:
        summarization_section()
    sidebar_info()

if __name__ == "__main__":
    main()
