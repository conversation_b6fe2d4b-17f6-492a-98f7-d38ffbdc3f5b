# RAG Application with Ollama

A complete Retrieval-Augmented Generation (RAG) application using Ollama with the locally available model llama3.1:latest. This application enables interactive chat and summarization functionality with PDF documents.

## Features

- 📄 **PDF Processing**: Load and process PDF files with intelligent text chunking
- 🔍 **Vector Search**: Generate embeddings using Ollama and store in Chroma vector database
- 💬 **Interactive Chat**: Chat with your documents using RAG-based question answering
- 🧠 **Conversation Memory**: Maintains context across conversation turns
- 📋 **Document Summarization**: Generate comprehensive summaries of uploaded documents
- 🎨 **Modern UI**: Clean Streamlit interface with tabbed navigation

## Prerequisites

1. **Ollama Installation**: Install Ollama on your system

   ```bash
   # macOS
   brew install ollama

   # Linux
   curl -fsSL https://ollama.ai/install.sh | sh
   ```

2. **Required Models**: Pull the necessary models

   ```bash
   ollama pull llama3.1:latest
   ollama pull nomic-embed-text
   ```

3. **Start Ollama Server**:
   ```bash
   ollama serve
   ```

## Installation

### Option 1: Automated Setup (Recommended)

1. **Clone or download this repository**

2. **Create virtual environment and install dependencies**:

   ```bash
   python create_environment.py
   ```

3. **Activate the environment**:

   ```bash
   # On macOS/Linux
   ./activate_env.sh

   # On Windows
   activate_env.bat

   # Or manually
   source rag_env/bin/activate  # macOS/Linux
   rag_env\Scripts\activate     # Windows
   ```

### Option 2: Manual Setup

1. **Clone or download this repository**

2. **Create virtual environment**:

   ```bash
   python -m venv rag_env
   ```

3. **Activate virtual environment**:

   ```bash
   # On macOS/Linux
   source rag_env/bin/activate

   # On Windows
   rag_env\Scripts\activate
   ```

4. **Install Python dependencies**:

   ```bash
   pip install --upgrade pip
   pip install -r requirements.txt
   ```

5. **Verify directory structure**:
   ```
   LLM RAG/
   ├── requirements.txt
   ├── config.py
   ├── app.py
   ├── src/
   │   ├── __init__.py
   │   ├── pdf_processor.py
   │   ├── embeddings.py
   │   ├── vector_store.py
   │   ├── retrieval_qa.py
   │   └── utils.py
   ├── data/
   │   └── uploads/
   └── chroma_db/
   ```

## Usage

1. **Activate the virtual environment** (if not already activated):

   ```bash
   # On macOS/Linux
   source rag_env/bin/activate

   # On Windows
   rag_env\Scripts\activate
   ```

2. **Test the setup** (optional but recommended):

   ```bash
   python test_setup.py
   ```

3. **Start the application**:

   ```bash
   streamlit run app.py
   ```

4. **Open your browser** and navigate to the displayed URL (typically `http://localhost:8501`)

5. **Upload PDF documents**:

   - Go to the "📁 Upload" tab
   - Select one or more PDF files
   - Click "Process" for each file

6. **Chat with documents**:

   - Switch to the "💬 Chat" tab
   - Ask questions about your uploaded documents
   - View sources and references for each answer

7. **Generate summaries**:
   - Go to the "📋 Summary" tab
   - Click "Generate Summary" to get an overview of all documents

## Configuration

The application can be configured by modifying `config.py`:

```python
# Ollama Configuration
OLLAMA_BASE_URL = "http://localhost:11434"
OLLAMA_MODEL = "llama3.1:latest"
OLLAMA_EMBEDDING_MODEL = "nomic-embed-text"

# Text Processing
CHUNK_SIZE = 1000
CHUNK_OVERLAP = 200
MAX_TOKENS = 4000

# File Upload
MAX_FILE_SIZE_MB = 50
```

## API Keys and Sensitive Data

The application includes placeholders for API keys in `config.py`:

```python
# API Keys (Leave blank - to be filled by user)
OPENAI_API_KEY = ""  # For potential OpenAI integration
HUGGINGFACE_API_KEY = ""  # For potential HuggingFace integration
```

**Note**: Currently, the application uses only Ollama (local models), so no external API keys are required.

## Architecture

### Components

1. **PDF Processor** (`src/pdf_processor.py`):

   - Extracts text from PDF files
   - Splits text into manageable chunks
   - Handles metadata and document structure

2. **Embeddings** (`src/embeddings.py`):

   - Integrates with Ollama embeddings API
   - Generates vector representations of text
   - Compatible with LangChain framework

3. **Vector Store** (`src/vector_store.py`):

   - Manages Chroma vector database
   - Handles document storage and retrieval
   - Supports similarity search and filtering

4. **Retrieval QA** (`src/retrieval_qa.py`):

   - Implements RAG chain with conversation memory
   - Integrates Ollama LLM with vector retrieval
   - Manages chat history and context

5. **Utilities** (`src/utils.py`):
   - Helper functions for file handling
   - System status checks
   - UI utilities and formatting

### Data Flow

1. **Document Upload**: PDF → Text Extraction → Chunking
2. **Embedding**: Text Chunks → Ollama Embeddings → Vector Store
3. **Query Processing**: User Question → Vector Search → Context Retrieval
4. **Response Generation**: Context + Question → Ollama LLM → Answer + Sources

## Troubleshooting

### Common Issues

1. **Ollama Connection Error**:

   - Ensure Ollama is running: `ollama serve`
   - Check if models are available: `ollama list`
   - Verify the base URL in config.py

2. **Model Not Found**:

   ```bash
   ollama pull llama3.1:latest
   ollama pull nomic-embed-text
   ```

3. **Memory Issues**:

   - Reduce `CHUNK_SIZE` in config.py
   - Process fewer documents at once
   - Restart the application

4. **PDF Processing Errors**:
   - Ensure PDF files are not corrupted
   - Check file size limits
   - Try with different PDF files

### System Requirements

- **RAM**: Minimum 8GB (16GB recommended for larger models)
- **Storage**: At least 10GB free space for models and data
- **Python**: 3.8 or higher

## Future Enhancements

The modular architecture supports easy extensions:

- 🔌 **Multiple LLM Support**: Add OpenAI, Anthropic, or other providers
- 🌐 **Web Scraping**: Process web pages and articles
- 📊 **Advanced Analytics**: Document insights and statistics
- 🔒 **Authentication**: User management and access control
- 📱 **Mobile Interface**: Responsive design improvements
- 🔄 **Batch Processing**: Handle multiple documents simultaneously

## License

This project is open source and available under the MIT License.

## Support

For issues and questions:

1. Check the troubleshooting section
2. Verify Ollama installation and model availability
3. Review application logs for detailed error messages
